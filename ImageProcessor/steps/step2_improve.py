from typing import Tuple

import cv2
import numpy as np

def auto_tone_mapping(image: cv2.Mat, low_percent: float = 1.0, high_percent: float = 99.0, gamma: float = None) -> cv2.Mat:
    low_val = np.percentile(image, low_percent)
    high_val = np.percentile(image, high_percent)
    stretched = np.clip((image - low_val) * 255.0 / (high_val - low_val), 0, 255).astype(np.uint8)
    if gamma is not None and gamma != 1.0:
        inv_gamma = 1.0 / gamma
        table = np.array([(i / 255.0) ** inv_gamma * 255 for i in range(256)]).astype("uint8")
        stretched = cv2.LUT(stretched, table)
    return stretched

def clahe(image: np.ndarray, clip_limit: float = 2.0, tile_grid_size: Tuple[int, int] = (8, 8)) -> np.ndarray:
    clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=tile_grid_size)
    return clahe.apply(image)

def unsharp_mask(image: np.ndarray, ksize: Tuple[int, int] = (5, 5), alpha: float = 1.5, beta: float = -0.5,
                 gamma: float = 0) -> np.ndarray:
    blurred = cv2.GaussianBlur(image, ksize, 0)
    return cv2.addWeighted(image, alpha, blurred, beta, gamma)

def denoise(image: np.ndarray, h: int = 10) -> np.ndarray:
    return cv2.fastNlMeansDenoising(image, None, h, 7, 21)

# Tunable parameters for StepTuner (min, max, default)
auto_tone_mapping.__tunable_params__ = {
    "low_percent": (0.0, 10.0, 1.0),
    "high_percent": (90.0, 100.0, 99.0),
    "gamma": (0.5, 3.0, 1.0),
}

clahe.__tunable_params__ = {
    "clip_limit": (0.1, 10.0, 2.0),
    "tile_w": (2, 32, 8),
    "tile_h": (2, 32, 8),
}

unsharp_mask.__tunable_params__ = {
    "ksize_w": (1, 31, 5),
    "ksize_h": (1, 31, 5),
    "alpha": (0.0, 3.0, 1.5),
    "beta": (-1.0, 0.0, -0.5),
    "gamma": (0.0, 50.0, 0.0),
}

denoise.__tunable_params__ = {
    "h": (1, 50, 10),
}


# --- Adapter functions for StepTuner (img, params_dict) -> BGR visualization ---

def tune_auto_tone_mapping(img: np.ndarray, p: dict) -> np.ndarray:
    low = float(p.get("low_percent", 1.0))
    high = float(p.get("high_percent", 99.0))
    gamma = float(p.get("gamma", 1.0))
    out = auto_tone_mapping(img, low_percent=low, high_percent=high, gamma=gamma)
    return cv2.cvtColor(out, cv2.COLOR_GRAY2BGR)


def tune_clahe(img: np.ndarray, p: dict) -> np.ndarray:
    clip = float(p.get("clip_limit", 2.0))
    tw = max(2, int(p.get("tile_w", 8)))
    th = max(2, int(p.get("tile_h", 8)))
    out = clahe(img, clip_limit=clip, tile_grid_size=(tw, th))
    return cv2.cvtColor(out, cv2.COLOR_GRAY2BGR)


def tune_unsharp_mask(img: np.ndarray, p: dict) -> np.ndarray:
    kw = max(1, int(p.get("ksize_w", 5))) | 1
    kh = max(1, int(p.get("ksize_h", 5))) | 1
    alpha = float(p.get("alpha", 1.5))
    beta = float(p.get("beta", -0.5))
    g = float(p.get("gamma", 0.0))
    out = unsharp_mask(img, ksize=(kw, kh), alpha=alpha, beta=beta, gamma=g)
    return cv2.cvtColor(out, cv2.COLOR_GRAY2BGR)


def tune_denoise(img: np.ndarray, p: dict) -> np.ndarray:
    h = int(p.get("h", 10))
    out = denoise(img, h=h)
    return cv2.cvtColor(out, cv2.COLOR_GRAY2BGR)

# Re-use underlying tuning specs
try:
    tune_auto_tone_mapping.__tunable_params__ = auto_tone_mapping.__tunable_params__
    tune_clahe.__tunable_params__ = clahe.__tunable_params__
    tune_unsharp_mask.__tunable_params__ = unsharp_mask.__tunable_params__
    tune_denoise.__tunable_params__ = denoise.__tunable_params__
except Exception:
    pass
