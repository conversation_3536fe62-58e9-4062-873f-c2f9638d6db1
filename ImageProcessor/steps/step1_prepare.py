import cv2
import numpy as np


def to_grayscale(image: cv2.Mat) -> cv2.Mat:
    if len(image.shape) == 3:
        return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    return image

def normalize_to_300dpi(img: cv2.Mat, threshold=0.15):

    height, width = img.shape[:2]

    # Urči menš<PERSON> a větší stranu (nezávisle na orientaci)
    min_side = min(width, height)
    max_side = max(width, height)
    current_aspect = max_side / min_side

    # Očekávané velikosti pro 300 DPI
    a4_min = 2480  # A4 menší
    a4_max = 3508  # A4 větší
    a4_aspect = a4_max / a4_min  # ~1.4145

    letter_min = 2550  # Letter menší
    letter_max = 3300  # Letter větší
    letter_aspect = letter_max / letter_min  # ~1.2941

    # Detekuj formát podle nejbližšího aspect ratio
    diff_a4 = abs(current_aspect - a4_aspect)
    diff_letter = abs(current_aspect - letter_aspect)

    if diff_a4 < diff_letter:
        format_type = 'A4'
        expected_min = a4_min
        expected_max = a4_max
    else:
        format_type = 'Letter'
        expected_min = letter_min
        expected_max = letter_max

    # Spočítej relativní odchylky
    dev_min = abs(min_side - expected_min) / expected_min
    dev_max = abs(max_side - expected_max) / expected_max
    avg_dev = (dev_min + dev_max) / 2

    if avg_dev > threshold:
        # Spočítej škálovací faktory pro obě strany
        scale_min = expected_min / min_side
        scale_max = expected_max / max_side
        avg_scale = (scale_min + scale_max) / 2

        # Resize s průměrným škálováním (zachová aspect, použij kvalitní interpolaci)
        new_width = int(width * avg_scale)
        new_height = int(height * avg_scale)
        resized_img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        return resized_img
    else:
        # Bez změny
        return img

# Tunable parameters for StepTuner (min, max, default)
normalize_to_300dpi.__tunable_params__ = {
    "threshold": (0.0, 0.5, 0.15),
}

def correct_perspective(image):
    # Upravená korekce perspektivy pomocí HoughLines pro detekci nejdelších linií na okrajích
    # Krok 1: Detekce okrajů
    blurred = cv2.GaussianBlur(image, (15, 15), 0)
    edges = cv2.Canny(blurred, 113, 104)

    # Krok 2: Detekce linií pomocí HoughLines
    lines = cv2.HoughLines(edges, 1, np.pi / 180, 180)  # Threshold 200, upravte podle potřeby

    if lines is None:
        return image  # Pokud žádné linie, vrátit nezměněné

    # Jednoduché filtrování linek podle úhlu a délky úsečky v obrazu
    H, W = image.shape[:2]
    tol = np.deg2rad(15.0)
    min_h_len = 1800.0
    min_v_len = 2800.0

    def _segment_in_image(rho, theta, W, H):
        cos_t, sin_t = np.cos(theta), np.sin(theta)
        pts = []
        eps = 1e-6
        # průnik s hranami obrazu
        # x = 0
        if abs(sin_t) > eps:
            y = (rho - 0 * cos_t) / sin_t
            if 0 <= y <= H - 1:
                pts.append((0, int(round(y))))
        # x = W-1
        if abs(sin_t) > eps:
            y = (rho - (W - 1) * cos_t) / sin_t
            if 0 <= y <= H - 1:
                pts.append((W - 1, int(round(y))))
        # y = 0
        if abs(cos_t) > eps:
            x = (rho - 0 * sin_t) / cos_t
            if 0 <= x <= W - 1:
                pts.append((int(round(x)), 0))
        # y = H-1
        if abs(cos_t) > eps:
            x = (rho - (H - 1) * sin_t) / cos_t
            if 0 <= x <= W - 1:
                pts.append((int(round(x)), H - 1))
        # odstraň duplicitní body
        uniq = []
        for pt in pts:
            if pt not in uniq:
                uniq.append(pt)
        if len(uniq) >= 2:
            return uniq[0], uniq[1]
        return None, None

    filtered = []
    for line in lines:
        rho, theta = line[0]
        d_h = min(abs(theta - 0.0), abs(theta - np.pi))
        d_v = abs(theta - (np.pi / 2))
        if (d_h > tol) and (d_v > tol):
            continue
        orient_h = d_h <= d_v
        p1, p2 = _segment_in_image(rho, theta, W, H)
        if p1 is None or p2 is None:
            continue
        length = float(np.hypot(p2[0] - p1[0], p2[1] - p1[1]))
        if orient_h:
            if length < min_h_len:
                continue
        else:
            if length < min_v_len:
                continue
        filtered.append(line)

    if len(filtered) == 0:
        return image

    lines = filtered

    # Funkce pro výpočet dvou bodů na linii pro výpočet průměrné pozice
    def get_line_points(rho, theta):
        a = np.cos(theta)
        b = np.sin(theta)
        x0 = a * rho
        y0 = b * theta
        pt1 = (int(x0 + 1000 * (-b)), int(y0 + 1000 * a))
        pt2 = (int(x0 - 1000 * (-b)), int(y0 - 1000 * a))
        return pt1, pt2

    # Rozdělení linií na horizontální a vertikální, a výpočet průměrné pozice
    horiz_lines = []
    vert_lines = []
    for line in lines:
        rho, theta = line[0]
        pt1, pt2 = get_line_points(rho, theta)
        if theta < np.pi / 4 or theta > 3 * np.pi / 4:  # Quasi-horizontální
            avg_y = (pt1[1] + pt2[1]) / 2
            horiz_lines.append((rho, theta, avg_y))
        else:  # Quasi-vertikální
            avg_x = (pt1[0] + pt2[0]) / 2
            vert_lines.append((rho, theta, avg_x))

    # Vybrat opačné strany: pro horiz - min a max avg_y (horní a dolní)
    if len(horiz_lines) < 2 or len(vert_lines) < 2:
        return image
    horiz_lines.sort(key=lambda x: x[2])  # Seřadit podle avg_y
    top_h = horiz_lines[0][:2]  # min avg_y - horní
    bottom_h = horiz_lines[-1][:2]  # max avg_y - dolní

    vert_lines.sort(key=lambda x: x[2])  # Seřadit podle avg_x
    left_v = vert_lines[0][:2]  # min avg_x - levá
    right_v = vert_lines[-1][:2]  # max avg_x - pravá

    # Funkce pro výpočet průsečíku dvou linií
    def line_intersection(line1, line2):
        rho1, theta1 = line1
        rho2, theta2 = line2
        A = np.array([
            [np.cos(theta1), np.sin(theta1)],
            [np.cos(theta2), np.sin(theta2)]
        ])
        b = np.array([[rho1], [rho2]])
        try:
            x0, y0 = np.linalg.solve(A, b)
            return (int(np.round(x0)), int(np.round(y0)))
        except:
            return None

    # Výpočet 4 průsečíků
    tl = line_intersection(top_h, left_v)
    tr = line_intersection(top_h, right_v)
    br = line_intersection(bottom_h, right_v)
    bl = line_intersection(bottom_h, left_v)

    intersections = [tl, tr, br, bl]
    if None in intersections:
        return image  # Pokud nějaký průsečík chybí

    # Seřadit rohy
    pts = np.array(intersections, dtype="float32")
    rect = np.zeros((4, 2), dtype="float32")

    s = pts.sum(axis=1)
    rect[0] = pts[np.argmin(s)]  # top-left
    rect[2] = pts[np.argmax(s)]  # bottom-right

    diff = np.diff(pts, axis=1)
    rect[1] = pts[np.argmax(diff)]  # top-right
    rect[3] = pts[np.argmin(diff)]  # bottom-left
    # Jednoduché filtrování linek podle úhlu a délky úsečky v obrazu (laditelné)
    H, W = gray.shape[:2]
    tol = np.deg2rad(float(p.get("angle_tol_deg", 15.0)))
    min_h_len = float(p.get("min_horiz_len", 1800))
    min_v_len = float(p.get("min_vert_len", 2800))

    def _segment_in_image(rho, theta, W, H):
        cos_t, sin_t = np.cos(theta), np.sin(theta)
        pts = []
        eps = 1e-6
        # průnik s hranami obrazu
        # x = 0
        if abs(sin_t) > eps:
            y = (rho - 0 * cos_t) / sin_t
            if 0 <= y <= H - 1:
                pts.append((0, int(round(y))))
        # x = W-1
        if abs(sin_t) > eps:
            y = (rho - (W - 1) * cos_t) / sin_t
            if 0 <= y <= H - 1:
                pts.append((W - 1, int(round(y))))
        # y = 0
        if abs(cos_t) > eps:
            x = (rho - 0 * sin_t) / cos_t
            if 0 <= x <= W - 1:
                pts.append((int(round(x)), 0))
        # y = H-1
        if abs(cos_t) > eps:
            x = (rho - (H - 1) * sin_t) / cos_t
            if 0 <= x <= W - 1:
                pts.append((int(round(x)), H - 1))
        # odstraň duplicitní body
        uniq = []
        for pt in pts:
            if pt not in uniq:
                uniq.append(pt)
        if len(uniq) >= 2:
            return uniq[0], uniq[1]
        return None, None

    filtered = []
    for line in lines:
        rho, theta = line[0]
        is_h = min(abs(theta - 0.0), abs(theta - np.pi)) <= tol
        is_v = abs(theta - (np.pi / 2)) <= tol
        if not (is_h or is_v):
            continue
        p1, p2 = _segment_in_image(rho, theta, W, H)
        if p1 is None or p2 is None:
            continue
        length = float(np.hypot(p2[0] - p1[0], p2[1] - p1[1]))
        if (is_h and length < min_h_len) or (is_v and length < min_v_len):
            continue
        filtered.append(line)

    if len(filtered) == 0:
        return vis

    lines = filtered


    # Vypočítat cílový obdélník
    (tl, tr, br, bl) = rect
    widthA = np.sqrt(((br[0] - bl[0]) ** 2) + ((br[1] - bl[1]) ** 2))
    widthB = np.sqrt(((tr[0] - tl[0]) ** 2) + ((tr[1] - tl[1]) ** 2))
    max_width = max(int(widthA), int(widthB))

    heightA = np.sqrt(((tr[0] - br[0]) ** 2) + ((tr[1] - br[1]) ** 2))
    heightB = np.sqrt(((tl[0] - bl[0]) ** 2) + ((tl[1] - bl[1]) ** 2))
    max_height = max(int(heightA), int(heightB))

    dst_pts = np.array([
        [0, 0],
        [max_width - 1, 0],
        [max_width - 1, max_height - 1],
        [0, max_height - 1]
    ], dtype="float32")

    # Perspektivní transformace
    M = cv2.getPerspectiveTransform(rect, dst_pts)
    warped = cv2.warpPerspective(image, M, (max_width, max_height))

    return warped

# Optional tuning hooks for perspective correction
correct_perspective.__tunable_params__ = {
    "blur_ksize": (3, 31, 15),
    "canny_low": (10, 200, 113),
    "canny_high": (50, 300, 104),
    "hough_threshold": (50, 400, 220),
}

# --- Adapter functions for StepTuner (img, params_dict) -> BGR visualization ---

def tune_normalize_to_300dpi(img: np.ndarray, p: dict) -> np.ndarray:
    thr = float(p.get("threshold", 0.15))
    out = normalize_to_300dpi(img, threshold=thr)
    return cv2.cvtColor(out, cv2.COLOR_GRAY2BGR)


def tune_correct_perspective(image: np.ndarray, p: dict) -> np.ndarray:
    # Ensure grayscale
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image

    k = max(1, int(p.get("blur_ksize", 5))) | 1
    low = int(p.get("canny_low", 50))
    high = int(p.get("canny_high", 150))
    hough_thr = int(p.get("hough_threshold", 200))

    blurred = cv2.GaussianBlur(gray, (k, k), 0)
    edges = cv2.Canny(blurred, low, high)

    # HoughLines for line detection
    lines = cv2.HoughLines(edges, 1, np.pi / 180, hough_thr)
    vis = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
    if lines is None:
        return vis
    # Angle/length filtering (tunable)
    H, W = gray.shape[:2]
    tol = np.deg2rad(float(p.get("angle_tol_deg", 15.0)))
    min_h_len = float(p.get("min_horiz_len", 1800))
    min_v_len = float(p.get("min_vert_len", 2800))

    def _segment_in_image(rho, theta, W, H):
        cos_t, sin_t = np.cos(theta), np.sin(theta)
        pts = []
        eps = 1e-6
        # x = 0
        if abs(sin_t) > eps:
            y = (rho - 0 * cos_t) / sin_t
            if 0 <= y <= H - 1:
                pts.append((0, int(round(y))))
        # x = W-1
        if abs(sin_t) > eps:
            y = (rho - (W - 1) * cos_t) / sin_t
            if 0 <= y <= H - 1:
                pts.append((W - 1, int(round(y))))
        # y = 0
        if abs(cos_t) > eps:
            x = (rho - 0 * sin_t) / cos_t
            if 0 <= x <= W - 1:
                pts.append((int(round(x)), 0))
        # y = H-1
        if abs(cos_t) > eps:
            x = (rho - (H - 1) * sin_t) / cos_t
            if 0 <= x <= W - 1:
                pts.append((int(round(x)), H - 1))
        # unique
        uniq = []
        for pt in pts:
            if pt not in uniq:
                uniq.append(pt)
        if len(uniq) >= 2:
            return uniq[0], uniq[1]
        return None, None

    filtered = []
    for line in lines:
        rho, theta = line[0]
        d_h = min(abs(theta - 0.0), abs(theta - np.pi))
        d_v = abs(theta - (np.pi / 2))
        if (d_h > tol) and (d_v > tol):
            continue
        orient_h = d_h <= d_v
        p1, p2 = _segment_in_image(rho, theta, W, H)
        if p1 is None or p2 is None:
            continue
        length = float(np.hypot(p2[0] - p1[0], p2[1] - p1[1]))
        if orient_h:
            if length < min_h_len:
                continue
        else:
            if length < min_v_len:
                continue
        filtered.append(line)

    if len(filtered) == 0:
        return vis
    lines = filtered


    def get_line_points(rho, theta):
        a = np.cos(theta)
        b = np.sin(theta)
        x0 = a * rho
        y0 = b * rho
        pt1 = (int(x0 + 1000 * (-b)), int(y0 + 1000 * a))
        pt2 = (int(x0 - 1000 * (-b)), int(y0 - 1000 * a))
        return pt1, pt2

    # Separate approx horizontal/vertical
    horiz_lines = []
    vert_lines = []
    for line in lines:
        rho, theta = line[0]
        pt1, pt2 = get_line_points(rho, theta)
        if theta < np.pi / 4 or theta > 3 * np.pi / 4:
            avg_y = (pt1[1] + pt2[1]) / 2
            horiz_lines.append((rho, theta, avg_y))
        else:
            avg_x = (pt1[0] + pt2[0]) / 2
            vert_lines.append((rho, theta, avg_x))

    if len(horiz_lines) < 2 or len(vert_lines) < 2:
        return vis

    horiz_lines.sort(key=lambda x: x[2])
    top_h = horiz_lines[0][:2]
    bottom_h = horiz_lines[-1][:2]

    vert_lines.sort(key=lambda x: x[2])
    left_v = vert_lines[0][:2]
    right_v = vert_lines[-1][:2]

    def line_intersection(line1, line2):
        rho1, theta1 = line1
        rho2, theta2 = line2
        A = np.array([[np.cos(theta1), np.sin(theta1)], [np.cos(theta2), np.sin(theta2)]])
        b = np.array([[rho1], [rho2]])
        try:
            x0, y0 = np.linalg.solve(A, b)
            return (int(np.round(x0)), int(np.round(y0)))
        except Exception:
            return None

    tl = line_intersection(top_h, left_v)
    tr = line_intersection(top_h, right_v)
    br = line_intersection(bottom_h, right_v)
    bl = line_intersection(bottom_h, left_v)

    if None in (tl, tr, br, bl):
        return vis

    # Draw detected rectangle for visualization
    for a, b in ((tl, tr), (tr, br), (br, bl), (bl, tl)):
        cv2.line(vis, a, b, (0, 255, 0), 2)

    return vis

# Re-use underlying tuning specs
try:
    # tune_normalize_to_300dpi.__tunable_params__ = normalize_to_300dpi.__tunable_params__
    # Explicit tunables for perspective tuning (including filtering limits)
    tune_correct_perspective.__tunable_params__ = {
        "blur_ksize": (3, 31, 13),
        "canny_low": (10, 200, 70),
        "canny_high": (50, 300, 140),
        "hough_threshold": (50, 400, 300),
        "min_horiz_len": (0, 6000, 1800),
        "min_vert_len": (0, 6000, 2800),
        "angle_tol_deg": (1.0, 45.0, 15.0),
    }
except Exception:
    pass



